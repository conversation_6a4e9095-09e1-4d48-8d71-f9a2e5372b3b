<?xml version="1.0" encoding="UTF-8" ?>
<?ccsproject version="1.0"?>
<projectOptions>
	<ccsVariant value="50:Theia-based"/>
	<ccsVersion value="51.5.0"/>
	<deviceFamily value="TMS470"/>
	<connection value="common/targetdb/connections/segger_j-link_connection.xml"/>
	<createSlaveProjects value=""/>
	<ignoreDefaultDeviceSettings value="true"/>
	<ignoreDefaultCCSSettings value="true"/>
	<templateProperties value="id=empty_LP_MSPM0G3507_nortos_ticlang.projectspec.test1,buildProfile=release,isHybrid=true"/>
	<activeTargetConfiguration value="targetConfigs/MSPM0G3507.ccxml"/>
	<isTargetConfigurationManual value="false"/>
	<sourceLookupPath value="${COM_TI_MSPM0_SDK_INSTALL_DIR}/source/ti/driverlib"/>
	<origin value="D:\ti\ccstheia151\mspm0_sdk_2_05_01_00\examples\nortos\LP_MSPM0G3507\driverlib\empty\ticlang\empty_LP_MSPM0G3507_nortos_ticlang.projectspec"/>
	<filesToOpen value="README.md,empty.syscfg"/>
</projectOptions>
