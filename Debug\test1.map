******************************************************************************
            TI ARM Clang Linker PC v4.0.0                      
******************************************************************************
>> Linked Thu Jul 31 10:12:54 2025

OUTPUT FILE NAME:   <test1.out>
ENTRY POINT SYMBOL: "_c_int00_noinit_noargs"  address: 0000014d


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00020000  000001a8  0001fe58  R  X
  SRAM                  20200000   00008000  00000200  00007e00  RW X
  BCR_CONFIG            41c00000   000000ff  00000000  000000ff  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    000001a8   000001a8    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    000000e8   000000e8    r-x .text
20207e00    20207e00    00000200   00000000    rw-
  20207e00    20207e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    000000e8     
                  000000c0    00000040     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  00000100    0000002c     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  0000012c    00000020     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  0000014c    00000020     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noinit_noargs)
                  0000016c    00000014     empty.o (.text.main)
                  00000180    00000010     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  00000190    0000000a     driverlib.a : dl_common.o (.text.DL_Common_delayCycles)
                  0000019a    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  0000019e    00000004     libc.a : pre_init.c.obj (.text._system_pre_init)
                  000001a2    00000004            : exit.c.obj (.text:abort)
                  000001a6    00000002     startup_mspm0g350x_ticlang.o (.text.Default_Handler)

.cinit     0    00000000    00000000     

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.stack     0    20207e00    00000200     UNINITIALIZED
                  20207e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20207e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                         code   ro data   rw data
       ------                         ----   -------   -------
    .\
       startup_mspm0g350x_ticlang.o   6      192       0      
       ti_msp_dl_config.o             156    0         0      
       empty.o                        20     0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         182    192       0      
                                                              
    D:/ti/ccstheia151/mspm0_sdk_2_05_01_00/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_common.o                    10     0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         10     0         0      
                                                              
    D:\ti\ccstheia151\ccs\tools\compiler\ti-cgt-armllvm_4.0.0.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       boot_cortex_m.c.obj            32     0         0      
       exit.c.obj                     4      0         0      
       pre_init.c.obj                 4      0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         40     0         0      
                                                              
       Stack:                         0      0         512    
    +--+------------------------------+------+---------+---------+
       Grand Total:                   232    192       512    


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                    
-------   ----                    
000001a7  ADC0_IRQHandler         
000001a7  ADC1_IRQHandler         
000001a7  AES_IRQHandler          
000001a2  C$$EXIT                 
000001a7  CANFD0_IRQHandler       
000001a7  DAC0_IRQHandler         
00000191  DL_Common_delayCycles   
000001a7  DMA_IRQHandler          
000001a7  Default_Handler         
000001a7  GROUP0_IRQHandler       
000001a7  GROUP1_IRQHandler       
000001a7  HardFault_Handler       
000001a7  I2C0_IRQHandler         
000001a7  I2C1_IRQHandler         
000001a7  NMI_Handler             
000001a7  PendSV_Handler          
000001a7  RTC_IRQHandler          
0000019b  Reset_Handler           
000001a7  SPI0_IRQHandler         
000001a7  SPI1_IRQHandler         
000001a7  SVC_Handler             
0000012d  SYSCFG_DL_GPIO_init     
000000c1  SYSCFG_DL_SYSCTL_init   
00000181  SYSCFG_DL_init          
00000101  SYSCFG_DL_initPower     
000001a7  SysTick_Handler         
000001a7  TIMA0_IRQHandler        
000001a7  TIMA1_IRQHandler        
000001a7  TIMG0_IRQHandler        
000001a7  TIMG12_IRQHandler       
000001a7  TIMG6_IRQHandler        
000001a7  TIMG7_IRQHandler        
000001a7  TIMG8_IRQHandler        
000001a7  UART0_IRQHandler        
000001a7  UART1_IRQHandler        
000001a7  UART2_IRQHandler        
000001a7  UART3_IRQHandler        
20208000  __STACK_END             
00000200  __STACK_SIZE            
00000000  __TI_ATRegion0_region_sz
00000000  __TI_ATRegion0_src_addr 
00000000  __TI_ATRegion0_trg_addr 
00000000  __TI_ATRegion1_region_sz
00000000  __TI_ATRegion1_src_addr 
00000000  __TI_ATRegion1_trg_addr 
00000000  __TI_ATRegion2_region_sz
00000000  __TI_ATRegion2_src_addr 
00000000  __TI_ATRegion2_trg_addr 
00000000  __TI_CINIT_Base         
00000000  __TI_CINIT_Limit        
00000000  __TI_CINIT_Warm         
ffffffff  __TI_pprof_out_hndl     
ffffffff  __TI_prof_data_size     
ffffffff  __TI_prof_data_start    
00000000  __TI_static_base__      
ffffffff  __binit__               
UNDEFED   __mpu_init              
20207e00  __stack                 
20200000  __start___llvm_prf_bits 
20200000  __start___llvm_prf_cnts 
20200000  __stop___llvm_prf_bits  
20200000  __stop___llvm_prf_cnts  
0000014d  _c_int00_noinit_noargs  
0000019f  _system_pre_init        
000001a3  abort                   
ffffffff  binit                   
00000000  interruptVectors        
0000016d  main                    


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                    
-------   ----                    
00000000  __TI_ATRegion0_region_sz
00000000  __TI_ATRegion0_src_addr 
00000000  __TI_ATRegion0_trg_addr 
00000000  __TI_ATRegion1_region_sz
00000000  __TI_ATRegion1_src_addr 
00000000  __TI_ATRegion1_trg_addr 
00000000  __TI_ATRegion2_region_sz
00000000  __TI_ATRegion2_src_addr 
00000000  __TI_ATRegion2_trg_addr 
00000000  __TI_CINIT_Base         
00000000  __TI_CINIT_Limit        
00000000  __TI_CINIT_Warm         
00000000  __TI_static_base__      
00000000  interruptVectors        
000000c1  SYSCFG_DL_SYSCTL_init   
00000101  SYSCFG_DL_initPower     
0000012d  SYSCFG_DL_GPIO_init     
0000014d  _c_int00_noinit_noargs  
0000016d  main                    
00000181  SYSCFG_DL_init          
00000191  DL_Common_delayCycles   
0000019b  Reset_Handler           
0000019f  _system_pre_init        
000001a2  C$$EXIT                 
000001a3  abort                   
000001a7  ADC0_IRQHandler         
000001a7  ADC1_IRQHandler         
000001a7  AES_IRQHandler          
000001a7  CANFD0_IRQHandler       
000001a7  DAC0_IRQHandler         
000001a7  DMA_IRQHandler          
000001a7  Default_Handler         
000001a7  GROUP0_IRQHandler       
000001a7  GROUP1_IRQHandler       
000001a7  HardFault_Handler       
000001a7  I2C0_IRQHandler         
000001a7  I2C1_IRQHandler         
000001a7  NMI_Handler             
000001a7  PendSV_Handler          
000001a7  RTC_IRQHandler          
000001a7  SPI0_IRQHandler         
000001a7  SPI1_IRQHandler         
000001a7  SVC_Handler             
000001a7  SysTick_Handler         
000001a7  TIMA0_IRQHandler        
000001a7  TIMA1_IRQHandler        
000001a7  TIMG0_IRQHandler        
000001a7  TIMG12_IRQHandler       
000001a7  TIMG6_IRQHandler        
000001a7  TIMG7_IRQHandler        
000001a7  TIMG8_IRQHandler        
000001a7  UART0_IRQHandler        
000001a7  UART1_IRQHandler        
000001a7  UART2_IRQHandler        
000001a7  UART3_IRQHandler        
00000200  __STACK_SIZE            
20200000  __start___llvm_prf_bits 
20200000  __start___llvm_prf_cnts 
20200000  __stop___llvm_prf_bits  
20200000  __stop___llvm_prf_cnts  
20207e00  __stack                 
20208000  __STACK_END             
ffffffff  __TI_pprof_out_hndl     
ffffffff  __TI_prof_data_size     
ffffffff  __TI_prof_data_start    
ffffffff  __binit__               
ffffffff  binit                   
UNDEFED   __mpu_init              

[68 symbols]
